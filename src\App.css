/* Responsive styles for the App */
.App {
  text-align: center;
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Mobile-first responsive design */
@media (max-width: 600px) {
  /* Small screens */
  .Mui<PERSON>ontainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .MuiPaper-root {
    margin: 4px !important;
  }

  .MuiTypography-h6 {
    font-size: 1rem !important;
  }

  .MuiButton-root {
    font-size: 0.8rem !important;
    padding: 6px 12px !important;
  }
}

@media (min-width: 601px) and (max-width: 900px) {
  /* Medium screens */
  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

@media (min-width: 901px) {
  /* Large screens */
  .MuiContainer-root {
    padding-left: 24px !important;
    padding-right: 24px !important;
  }
}

/* Responsive form elements */
@media (max-width: 600px) {
  .MuiFormControl-root {
    margin-bottom: 16px !important;
  }

  .MuiTextField-root {
    width: 100% !important;
  }

  .MuiRadioGroup-root {
    flex-direction: column !important;
  }

  .MuiFormControlLabel-root {
    margin-bottom: 8px !important;
  }
}

/* Responsive tables and grids */
@media (max-width: 600px) {
  .responsive-table {
    overflow-x: auto;
  }

  .responsive-grid {
    display: block !important;
  }

  .responsive-grid > * {
    width: 100% !important;
    margin-bottom: 16px !important;
  }
}

/* Logo responsiveness */
.responsive-logo {
  height: clamp(24px, 5vw, 60px);
  width: auto;
  max-width: 100%;
}

/* Header responsiveness */
.responsive-header {
  padding: 8px 16px;
}

@media (max-width: 600px) {
  .responsive-header {
    padding: 4px 8px;
    flex-wrap: wrap;
  }

  .responsive-header .MuiTypography-root {
    font-size: 0.9rem !important;
    line-height: 1.2 !important;
  }
}